<?xml version="1.0" encoding="utf-8"?>
<selector
  xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true" android:drawable="@color/tanx_browser_actionbar_menu_bg_pressed" />
    <item android:state_selected="true" android:state_pressed="true" android:drawable="@color/tanx_browser_actionbar_menu_bg_pressed" />
    <item android:state_selected="false" android:state_pressed="true" android:drawable="@color/tanx_browser_actionbar_menu_bg_pressed" />
    <item android:state_selected="true" android:state_pressed="false" android:drawable="@color/tanx_browser_actionbar_menu_bg_pressed" />
    <item android:drawable="@color/tanx_browser_actionbar_menu_bg" />
</selector>
