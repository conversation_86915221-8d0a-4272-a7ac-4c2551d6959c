<?xml version="1.0" encoding="utf-8"?>
<layer-list
  xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:gravity="center_vertical" android:id="@android:id/background" android:height="12.0dip">
        <shape>
            <solid android:color="#ffd8d8d8" />
            <corners android:radius="6.0dip" />
        </shape>
    </item>
    <item android:gravity="center_vertical" android:id="@android:id/secondaryProgress" android:height="12.0dip">
        <clip>
            <shape android:shape="rectangle">
                <gradient android:startColor="#fffe3661" android:endColor="#fff10528" android:useLevel="true" android:angle="0.0" android:type="linear" />
                <corners android:radius="6.0dip" />
            </shape>
        </clip>
    </item>
    <item android:gravity="center_vertical" android:id="@android:id/progress" android:height="12.0dip">
        <clip>
            <shape android:shape="rectangle">
                <gradient android:startColor="#ffd7000f" android:endColor="#ffd7000f" android:useLevel="true" android:angle="0.0" android:type="linear" />
                <corners android:radius="6.0dip" />
            </shape>
        </clip>
    </item>
</layer-list>
