<?xml version="1.0" encoding="utf-8"?>
<layer-list
  xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <padding android:left="1.0px" android:top="1.0px" android:right="1.0px" android:bottom="1.0px" />
            <solid android:color="#01000000" />
            <corners android:radius="7.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:left="1.0px" android:top="1.0px" android:right="1.0px" android:bottom="1.0px" />
            <solid android:color="#01000000" />
            <corners android:radius="7.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:left="1.0px" android:top="1.0px" android:right="1.0px" android:bottom="1.0px" />
            <solid android:color="#02000000" />
            <corners android:radius="6.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:left="1.0px" android:top="1.0px" android:right="1.0px" android:bottom="1.0px" />
            <solid android:color="#02000000" />
            <corners android:radius="6.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:left="1.0px" android:top="1.0px" android:right="1.0px" android:bottom="1.0px" />
            <solid android:color="#02000000" />
            <corners android:radius="6.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:left="1.0px" android:top="1.0px" android:right="1.0px" android:bottom="1.0px" />
            <solid android:color="#03000000" />
            <corners android:radius="6.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:left="1.0px" android:top="1.0px" android:right="1.0px" android:bottom="1.0px" />
            <solid android:color="#03000000" />
            <corners android:radius="5.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:left="1.0px" android:top="1.0px" android:right="1.0px" android:bottom="1.0px" />
            <solid android:color="#04000000" />
            <corners android:radius="5.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:left="1.0px" android:top="1.0px" android:right="1.0px" android:bottom="1.0px" />
            <solid android:color="#04000000" />
            <corners android:radius="5.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:left="1.0px" android:top="1.0px" android:right="1.0px" android:bottom="1.0px" />
            <solid android:color="#04000000" />
            <corners android:radius="4.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:left="1.0px" android:top="1.0px" android:right="1.0px" android:bottom="1.0px" />
            <solid android:color="#05000000" />
            <corners android:radius="4.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:left="1.0px" android:top="1.0px" android:right="1.0px" android:bottom="1.0px" />
            <solid android:color="#05000000" />
            <corners android:radius="3.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:left="1.0px" android:top="1.0px" android:right="1.0px" android:bottom="1.0px" />
            <solid android:color="#06000000" />
            <corners android:radius="3.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:left="1.0px" android:top="1.0px" android:right="1.0px" android:bottom="1.0px" />
            <solid android:color="#06000000" />
            <corners android:radius="2.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:left="1.0px" android:top="1.0px" android:right="1.0px" android:bottom="1.0px" />
            <solid android:color="#06000000" />
            <corners android:radius="2.0dip" />
        </shape>
    </item>
    <item>
        <shape>
            <padding android:left="1.0dip" android:top="1.0dip" android:right="1.0dip" android:bottom="1.0dip" />
            <solid android:color="#ffffffff" />
            <corners android:radius="2.0dip" />
        </shape>
    </item>
</layer-list>
