<?xml version="1.0" encoding="utf-8"?>
<selector
  xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <gradient android:startColor="@color/c_fe3661" android:endColor="@color/c_f10528" android:angle="0.0" />
            <corners android:radius="20.0dip" />
        </shape>
    </item>
    <item android:state_selected="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/c_cacad3" />
            <corners android:radius="20.0dip" />
        </shape>
    </item>
</selector>
